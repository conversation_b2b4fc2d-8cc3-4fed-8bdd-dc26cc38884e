"""
MCP服务配置页面 - 简化版本
"""
import json
import os
import streamlit as st
from functions.simple_mcp import simple_mcp_client, get_all_servers, get_server_status


def show_service_logs():
    """显示服务日志"""
    st.info("📝 简化版MCP客户端暂不支持详细日志显示")

    # 显示服务状态
    servers = get_all_servers()
    if servers:
        st.subheader("服务状态")
        for server_name, server_config in servers.items():
            if server_config.get("enabled", False):
                status = get_server_status(server_name)
                status_icon = {
                    "ready": "🟡",
                    "running": "🟢",
                    "disabled": "⚪",
                    "not_configured": "❓"
                }.get(status, "❓")

                st.write(f"{status_icon} **{server_name}** - {status}")
    else:
        st.info("暂无MCP服务")


def render_mcp_config_page():
    """渲染MCP配置页面"""
    # 获取所有服务器配置
    try:
        servers = get_all_servers()
    except Exception as e:
        st.error(f"获取MCP服务配置失败: {e}")
        return

    # === MCP 服务列表 ===
    st.header("MCP 服务列表")

    if not servers:
        st.info("暂无MCP服务")
    else:
        for server_name, server_config in servers.items():
            status = get_server_status(server_name)
            enabled = server_config.get("enabled", False)

            # 状态显示映射
            status_display = {
                "ready": "🟡",
                "running": "🟢",
                "disabled": "⚪",
                "not_configured": "❓"
            }.get(status, "❓")

            # 状态文本映射
            status_text = {
                "ready": "准备就绪",
                "running": "运行中",
                "disabled": "已禁用",
                "not_configured": "未配置"
            }.get(status, "未知")

            col1, col2 = st.columns([4, 1])

            with col1:
                st.write(f"{status_display} **{server_name}** - {status_text}")

            with col2:
                new_enabled = st.toggle("启用服务", value=enabled, key=f"toggle_{server_name}", label_visibility="collapsed")

                # 只有当状态发生变化时才执行操作
                if new_enabled != enabled:
                    # 更新配置文件
                    simple_mcp_client.config["mcpServers"][server_name]["enabled"] = new_enabled

                    # 保存配置
                    os.makedirs(os.path.dirname(simple_mcp_client.config_path), exist_ok=True)
                    with open(simple_mcp_client.config_path, 'w', encoding='utf-8') as f:
                        json.dump(simple_mcp_client.config, f, indent=2, ensure_ascii=False)

                    st.success(f"已{'启用' if new_enabled else '禁用'} {server_name}")
                    st.rerun()

    # === MCP服务日志 ===
    if servers:
        st.header("MCP服务日志")
        show_service_logs()

    # === MCP导入/导出配置 ===
    st.header("MCP导入/导出配置")

    col1, col2 = st.columns(2)

    with col1:
        # 导出配置
        if st.button("导出配置"):
            try:
                config_json = json.dumps(simple_mcp_client.config, indent=2, ensure_ascii=False)
                st.download_button(
                    label="下载配置文件",
                    data=config_json,
                    file_name="mcp_config.json",
                    mime="application/json"
                )
            except Exception as e:
                st.error(f"导出配置失败: {e}")

    with col2:
        # 导入配置
        uploaded_file = st.file_uploader("导入配置文件", type=["json"])
        if uploaded_file is not None:
            try:
                config = json.load(uploaded_file)
                if "mcpServers" in config:
                    # 更新配置
                    simple_mcp_client.config = config

                    # 保存配置
                    os.makedirs(os.path.dirname(simple_mcp_client.config_path), exist_ok=True)
                    with open(simple_mcp_client.config_path, 'w', encoding='utf-8') as f:
                        json.dump(config, f, indent=2, ensure_ascii=False)

                    st.success("配置导入成功")
                    st.rerun()
                else:
                    st.error("配置文件格式错误，缺少 mcpServers 字段")
            except json.JSONDecodeError:
                st.error("配置文件格式错误，请上传有效的JSON文件")
            except Exception as e:
                st.error(f"导入配置失败: {e}")




# 如果直接运行此文件，则显示配置页面
if __name__ == "__main__":
    render_mcp_config_page()
