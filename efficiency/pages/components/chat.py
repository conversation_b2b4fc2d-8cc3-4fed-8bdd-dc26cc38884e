import os
import re
import json
import logging
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from uuid import UUID, uuid4

import streamlit as st
from openai import OpenAI

from core.memory import Memory, MemoryManager
from functions.filesystem import *
from functions.mysql_interperter import execute_sql
from functions.simple_mcp import use_mcp_tool, get_available_tools, get_server_status, get_all_servers

# 全局abilities字典
abilities = {
    "execute_command": execute_command,
    "write_to_file": write_to_file,
    "read_file": read_file,
    "read_file_structure": read_file_structure,
    "list_files_in_directory": list_files_in_directory,
    "replace_in_file": replace_in_file,
    "search_files": search_files,
    "list_files": list_files,
    "list_code_definition_names": list_code_definition_names,
    "ask_followup_question": ask_followup_question,
    "attempt_completion": attempt_completion,
    "execute_sql": execute_sql,
    "use_mcp_tool": use_mcp_tool,
}

def check_mcp_availability():
    """检查MCP功能是否可用（至少有一个服务启用）"""
    try:
        for server_name, server_config in get_all_servers().items():
            if server_config.get("enabled", False):
                logging.info(f"发现启用的MCP服务: {server_name}")
                return True

        logging.info("没有发现启用的MCP服务")
        return False
    except Exception as e:
        logging.error(f"检查MCP可用性失败: {e}")
        return False


def get_relevant_memories(query: str, extract_memory_tag: bool = True) -> List[Memory]:
    """获取与查询相关的记忆

    Args:
        query: 查询内容

    Returns:
        相关的记忆列表
    """
    if extract_memory_tag:
        mem = re.search(r"<memory>(.*?)</memory>", query, flags=re.DOTALL)
        if mem:
            query = mem.group(1)
        else:
            return []

    memory_manager = st.session_state.get('memory_manager')

    search_labels = []
    for label in memory_manager.labels.values():
        if label.label in query:
            search_labels.append(label)
            continue

        if not label.synonyms:
            continue

        for synonym in label.synonyms.split(","):
            if synonym in query:
                search_labels.append(label)
                break

    logging.info("search_labels:" + str([label.label for label in search_labels]))
    relevant_memories = memory_manager.search_memory(query, [i.id for i in search_labels], k=5, search_type="label")
    logging.info(f"for query: {query}, relevant_memories: {relevant_memories}")
    return relevant_memories


def fetch_info_from_environment(environment: dict, prompt: str) -> str:
    """从环境变量中提取信息"""
    environment_info = ""
    for key, value in environment.items():
        if key in prompt:
            environment_info += f"{key} result: {value}\n"
    return environment_info


def build_system_prompt_with_memory(query: str, plan: str) -> str:
    """构建系统提示"""
    # fetch long-term memories by query
    relevant_memories = get_relevant_memories(query)

    # build relevant memories context
    memory_context = "\n# 和用户有关的记忆：\n"
    for memory in relevant_memories:
        memory_context += f"\ncreated at [{memory.created_at}]\nsummary:\n{memory.original_text}\n"

    if plan:
        memory_context += f"\n\n当前正在执行的计划：\n{plan}\n"

    prompt = memory_context
    return prompt


def xml_exact(llm_output: str) -> Tuple[str, str, Dict[str, str]]:
    """
    解析 LLM 的输出，提取工具调用信息

    Args:
        llm_output (str): LLM 生成的文本

    Returns:
        tuple: 是否成功, 工具名, 参数字典
    """

    # 匹配最外层工具调用标签（支持跨行匹配）
    tool_tag_match = re.findall(
        r'<(\w+)>(.*?)</\1>',  # 使用反向引用确保标签闭合
        llm_output,
        re.DOTALL  # 允许 . 匹配换行符
    )

    if not tool_tag_match:
        return "未检测到正确的工具调用", "attempt_completion", {}  # 未检测到工具调用

    tool_name, inner_content = tool_tag_match[-1]
    inner_content = inner_content.strip()  # 去除首尾空白

    # 解析参数标签（支持嵌套参数值中的XML字符，需LLM自行转义）
    param_pattern = re.compile(
        r'<(\w+)>(.*?)</\1>',  # 使用非贪婪匹配
        re.DOTALL
    )

    # 提取所有参数并存入字典
    params = {}
    for param_name, param_value in param_pattern.findall(inner_content):
        params[param_name] = param_value.strip()  # 去除参数值首尾空白

    # 检查内容合法性：所有内容都应该是参数标签
    sanitized_content = param_pattern.sub('', inner_content)  # 移除所有参数标签
    if sanitized_content.strip():  # 存在非参数内容
        print(f"sanitized_content: {sanitized_content}")
        return "存在非参数内容", "", {}

    return "参数解析成功", tool_name, params


def parse_simple_mcp_command(action: str) -> Tuple[str, str, dict]:
    """解析MCP命令

    支持的格式：
    - "使用files，查询/app/data"
    - "用files读取/app/config.json"
    - "files list /app"
    - "files read /app/test.txt"
    """
    import re

    # 模式1: 使用{server}，{action}{path}
    pattern1 = r"使用(\w+)，(查询|列出|读取|写入)(.+)"
    match1 = re.match(pattern1, action.strip())
    if match1:
        server_name = match1.group(1)
        action_type = match1.group(2)
        path = match1.group(3).strip('"\'')

        tool_mapping = {
            "查询": "list_directory",
            "列出": "list_directory",
            "读取": "read_file",
            "写入": "write_file"
        }

        tool_name = tool_mapping.get(action_type, "list_directory")
        params = {"server_name": server_name, "tool_name": tool_name, "arguments": {"path": path}}
        return "简化MCP命令解析成功", "use_mcp_tool", params

    # 模式2: {server} {action} {path}
    pattern2 = r"(\w+)\s+(list|read|write|ls|cat)\s+(.+)"
    match2 = re.match(pattern2, action.strip())
    if match2:
        server_name = match2.group(1)
        action_type = match2.group(2)
        path = match2.group(3).strip('"\'')

        tool_mapping = {
            "list": "list_directory",
            "ls": "list_directory",
            "read": "read_file",
            "cat": "read_file",
            "write": "write_file"
        }

        tool_name = tool_mapping.get(action_type, "list_directory")
        params = {"server_name": server_name, "tool_name": tool_name, "arguments": {"path": path}}
        return "简化MCP命令解析成功", "use_mcp_tool", params

    return "不是简化MCP命令", "", {}


def execute_action(action: str) -> Tuple[bool, str]:
    """
    Execute action

    Args:
        action (str): Action in workflow

    Returns:
        dict: Function call
    """
    is_end = False

    # 首先尝试解析MCP命令
    simple_message, simple_function, simple_params = parse_simple_mcp_command(action)
    if simple_function:
        message, function, params = simple_message, simple_function, simple_params
    else:
        # 如果不是简化命令，使用原有的XML解析
        try:
            message, function, params = xml_exact(action)
        except Exception as e:
            logging.error(f"Error parsing action: {e}")
            return is_end, f"Error parsing action: {e}"

    if not function:
        return is_end, message

    if function in ["ask_followup_question", "attempt_completion"]:
        is_end = True

    try:
        # 特殊处理MCP工具调用的arguments参数
        if function in ["use_mcp_tool", "access_mcp_resource"]:
            if "arguments" in params and isinstance(params["arguments"], str):
                try:
                    params["arguments"] = json.loads(params["arguments"])
                except json.JSONDecodeError as e:
                    logging.error(f"JSON解析失败: {e}")
                    result = f"JSON参数解析失败: {params['arguments']}, 错误: {e}"
                    return is_end, result

        # 🚨 MCP功能开关控制 - 真正阻止调用
        if function in ["use_mcp_tool"]:
            if not check_mcp_availability():
                # 真正阻止调用，返回错误信息
                result = "❌ MCP功能已关闭。请在MCP配置页面启用相关服务后再试。"
                logging.warning("MCP功能调用被阻止：没有启用的MCP服务")
                return is_end, result

        is_success, result = abilities.get(function)(**params)
        result = f"Function call: {function}, params: {params}, is_success: {is_success}, result: {result}"
    except Exception as e:
        logging.error(f"Error executing {function}: {e}")
        result = f"Error executing function: {function}, params: {params}, error: {e}"

    return is_end, result


def chat_with_conversation(user_input: str, system_input: str = "", history: List[Dict] = None) -> str:
    """与用户对话，包含记忆上下文"""
    # 记忆召回
    # system_memories = get_relevant_memories(system_input)
    # system_prompt = system_memories[0].original_text if system_memories else ""
    system_prompt = system_input
    with st.chat_message("user"):
        with st.expander("System Prompt"):
            st.write(system_prompt)
        st.write(user_input)

    # 如果存在 <memory> ... </memory> 标签，则提取标签内内容召回记忆
    user_memory_tags = re.findall(r"<memory>(.*?)</memory>", user_input, flags=re.DOTALL)
    if user_memory_tags:
        user_memories = get_relevant_memories(user_memory_tags[0])
        memory_context = "\n# 和用户相关信息：\n"
        for memory in user_memories:
            memory_context += f"\n---\n\n创建时间: {memory.created_at}]\n内容:\n{memory.original_text}\n"

        # 修改后：使用捕获组来保留标签
        user_input = re.sub(r"(<memory>).*?(</memory>)", r"\1" + memory_context + r"\2", user_input, flags=re.DOTALL)

    messages = [{"role": "system", "content": system_prompt}] + \
               [{"role": m["role"], "content": m["content"]} for m in history] + \
               [{"role": "user", "content": user_input}]

    # 初始化 OpenAI Chat 客户端
    chat_client = OpenAI(
        base_url=os.getenv("BASE_URL"),
        api_key=os.getenv("API_KEY")
    )

    # 生成并显示AI回复
    with st.chat_message("assistant"):
        try:
            stream = chat_client.chat.completions.create(
                model=os.getenv("MODEL"),
                messages=messages,
                stream=True,
            )
            response = st.write_stream(stream)
        except Exception as e:
            st.error(f"An error occurred: {str(e)}")
            logging.error(f"Chat error: {str(e)}", exc_info=True)
            return f"An error occurred: {str(e)}"

    return response


def think(content: str, history: List[Dict] = None) -> str:
    """LLM 调用
    """
    # 获取MCP服务列表
    enabled_servers = {}
    for server_name, server_config in get_all_servers().items():
        if server_config.get("enabled", False):
            enabled_servers[server_name] = server_config

    # 构建高级工具信息
    tools_info = "可用的高级工具:\n"
    if enabled_servers:
        for server_name in enabled_servers:
            tools_info += f"- {server_name}\n"
        tools_info += "\n注意：只能使用上述已启用的高级工具。\n"
    else:
        tools_info += "当前没有启用的高级工具。高级工具功能不可用，请不要尝试调用相关功能。\n"

    # 构建完整的工具列表（包括基础工具和高级工具）
    all_tools_info = f"""
## execute_command
描述：请求在系统上执行 CLI 命令。当您需要执行系统操作或运行特定命令来完成用户任务中的任何步骤时，请使用此命令。您必须根据用户的系统定制命令，并清楚地解释命令的作用。对于命令链，请使用适合用户 shell 的链式语法。最好执行复杂的 CLI 命令而不是创建可执行脚本，因为它们更灵活且更易于运行。命令将在当前工作目录中执行：/app
参数：
- command：（必需）要执行的 CLI 命令。这应该对当前操作系统有效。确保命令格式正确且不包含任何有害指令。
- require_approval：（必需）字符串，指示如果用户启用了自动批准模式，此命令是否需要用户明确批准才能执行。

## read_file
描述：请求读取指定路径下的文件内容。
参数：
- file_path：（必需）要读取的文件的路径

## write_to_file
描述：请求将内容写入指定路径的文件。如果文件存在，将用提供的内容覆盖它。
参数：
- path：（必需）要写入的文件的路径
- content：（必需）要写入文件的内容

## execute_sql
描述：可以使用该函数链接数据库，执行SQL查询并以Markdown格式返回结果
参数：
- sql_str：需要执行的sql语句

## use_mcp_tool
描述：使用高级工具执行特定任务，如文件操作、网络请求、浏览器自动化等
参数：
- server_name：（必需）高级工具服务名称
- tool_name：（必需）工具名称
- arguments：（必需）工具参数，JSON格式

{tools_info}
"""

    # 读取系统提示
    _, system_prompt = read_file("./examples/tp.md")

    # 替换动态变量
    system_prompt = system_prompt.replace("{tools}", all_tools_info)
    system_prompt = system_prompt.replace("{mcp_services}", tools_info)
    system_prompt = system_prompt.replace("{directory}", "/app")  # 设置当前工作目录

    # 🔧 调试：打印工具信息（简化版）
    print("=== 高级工具信息 ===")
    print(tools_info)
    print("=== 用户历史 ===")
    if history:
        print(f"sanitized_content: {history[-1].get('content', '')[:100]}...")
    else:
        print("无历史记录")

    response = chat_with_conversation(content, system_prompt, history)
    return response


def run(query: str, history: List[Dict] = None) -> str:
    """执行
    """
    user_response = query
    epoch = 1
    max_epoch = 10
    group_id = str(uuid4())
    while True:
        previous_ids = []
        for m in history:
            if m["role"] == "assistant":
                previous_ids.append(m.get("id"))
        # 记录对话历史
        llm_input = {
            "role": "user",
            "time": str(datetime.now()),
            "content": user_response,
            "id": "u_" + str(uuid4()),
            "group_id": group_id,
            "previous_ids": previous_ids,
        }
        st.session_state.session.conversation.append(llm_input)  # 记录到页面
        history.append(llm_input)  # 记录到下次对话

        # thinking
        response = think(user_response, history)

        llm_output = {
            "role": "assistant",
            "time": str(datetime.now()),
            "content": response,
            "id": "a_" + str(uuid4()),
            "group_id": group_id,
            "model": {
                "name": os.getenv("MODEL"),
            }
        }
        # 添加AI回复到消息记录
        st.session_state.session.conversation.append(llm_output)  # 记录到页面
        history.append(llm_output)  # 记录到下次对话

        st.session_state.session_manager.save(st.session_state.session)  # 保存会话

        # action
        is_end, execute_action_result = execute_action(response)

        # Show as user called the function
        user_response = execute_action_result
        with st.chat_message("user"):
            st.markdown(user_response)

        if is_end:
            break

        epoch += 1
        if epoch >= max_epoch:
            user_response = "最大迭代次数已达到，结束对话。\n最近一次 Reason 结果为：\n" + response + \
                            "最近一次 Action 结果为：\n" + user_response
            break

    return user_response
