{"mcpServers": {"files": {"command": "node", "args": ["/usr/lib/node_modules/@modelcontextprotocol/server-filesystem/dist/index.js", "."], "enabled": false}, "playwright": {"command": "npx", "args": ["-y", "@executeautomation/playwright-mcp-server"], "enabled": false}, "duckduckgo-remote": {"connection_type": "http", "server_name": "@nickclyde/duckduckgo-mcp-server", "api_key": "************************************", "profile_id": "", "config": {}, "enabled": true}, "chinarailway": {"connection_type": "http", "server_name": "@shenpeiheng/mcp-server-chinarailway", "api_key": "************************************", "profile_id": "", "config": {}, "enabled": true}, "amap-amap-sse": {"url": "https://mcp.amap.com/sse?key=2353fca3578857015155e233d135ae11", "enabled": true}}}