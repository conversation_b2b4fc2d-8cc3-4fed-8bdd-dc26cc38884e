# MCP 简化架构说明

## 概述

本项目已将复杂的MCP实现简化为基于官方文档的通用架构，删除了冗余代码，提供了统一的MCP服务接口。

## 架构变更

### 删除的文件
- `functions/universal_mcp.py` - 复杂的通用MCP接口
- `functions/intelligent_mcp.py` - 智能MCP代理
- `functions/mcp_server.py` - MCP服务端管理器
- `mcp_cli.py` - MCP命令行工具
- `mcp_bridge/` - Node.js桥接目录
- `mcp_servers/` - MCP服务器目录
- `test_*.py` - 各种测试文件

### 新增的文件
- `functions/simple_mcp.py` - 简化的MCP客户端实现

### 修改的文件
- `pages/components/chat.py` - 更新为使用简化的MCP接口
- `pages/mcp_config.py` - 简化的MCP配置页面
- `app.py` - 移除复杂的MCP服务启动逻辑

## 核心特性

### 1. 统一的MCP接口
```python
from functions.simple_mcp import use_mcp_tool

# 统一调用接口
success, result = use_mcp_tool(
    server_name="hotnews",
    tool_name="get_hot_news", 
    arguments={"source": 1}
)
```

### 2. 支持两种连接类型

#### STDIO连接（本地服务）
```json
{
  "files": {
    "command": "npx",
    "args": ["-y", "@modelcontextprotocol/server-filesystem", "/app"],
    "enabled": true,
    "connection_type": "stdio",
    "on_demand": true
  }
}
```

#### HTTP连接（远程服务）
```json
{
  "hotnews": {
    "connection_type": "http",
    "server_name": "@wopal/mcp-server-hotnews",
    "api_key": "your-api-key",
    "enabled": true
  }
}
```

### 3. 简化的配置管理
- 通过Web界面直接启用/禁用服务
- 支持配置导入/导出
- 自动状态检测

## 使用方法

### 1. 配置MCP服务
访问MCP配置页面，启用需要的服务：
- `files` - 文件系统操作
- `playwright` - 网页自动化
- `hotnews` - 热门新闻
- `chinarailway` - 火车票查询
- `duckduckgo-remote` - 网页搜索

### 2. 在对话中使用MCP
```xml
<use_mcp_tool>
<server_name>hotnews</server_name>
<tool_name>get_hot_news</tool_name>
<arguments>{"source": 1}</arguments>
</use_mcp_tool>
```

### 3. 简化命令格式
支持自然语言式的MCP命令：
- "使用files，查询/app/data"
- "files list /app"
- "files read /app/config.json"

## 技术实现

### SimpleMCPClient类
基于官方MCP文档实现的简化客户端：
- 统一处理STDIO和HTTP连接
- 自动资源管理
- 错误处理和重试机制

### 核心方法
- `connect_to_server()` - 连接到MCP服务器
- `list_tools()` - 列出可用工具
- `call_tool()` - 调用工具
- `cleanup()` - 清理资源

## 优势

1. **代码简洁** - 从多个复杂文件简化为单个文件
2. **易于维护** - 基于官方文档的标准实现
3. **通用性强** - 无需为每个服务编写定制代码
4. **配置驱动** - 通过JSON配置控制所有服务
5. **错误处理** - 统一的错误处理和日志记录

## 配置示例

完整的MCP配置文件示例：
```json
{
  "mcpServers": {
    "files": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem", "/app"],
      "enabled": true,
      "connection_type": "stdio",
      "on_demand": true
    },
    "hotnews": {
      "connection_type": "http",
      "server_name": "@wopal/mcp-server-hotnews",
      "api_key": "your-api-key",
      "enabled": true
    }
  }
}
```

## 注意事项

1. **API密钥** - HTTP服务需要有效的Smithery API密钥
2. **依赖项** - 确保安装了必要的MCP Python包
3. **权限** - STDIO服务可能需要特定的系统权限
4. **网络** - HTTP服务需要网络连接

## 故障排除

### 常见问题
1. **服务无法启动** - 检查配置文件格式和API密钥
2. **工具调用失败** - 确认服务已启用且参数正确
3. **连接超时** - 检查网络连接和服务状态

### 日志查看
在MCP配置页面查看服务状态和基本日志信息。

## 扩展新服务

添加新的MCP服务只需：
1. 在配置文件中添加服务配置
2. 在Web界面启用服务
3. 无需修改任何代码

这种设计实现了真正的配置驱动和通用性。
