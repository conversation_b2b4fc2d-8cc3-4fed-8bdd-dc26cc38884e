# -*- coding: utf-8 -*-
"""
@Time ： 2025/4/9 4:47 下午
@Auth ： Jinx
@File ：home.py.py
@IDE ：PyCharm
"""
import logging
import sys
import os

import streamlit as st

from pages import init_streamlit
from pages.components.sidebar import render_chat_sidebar
from pages.components.history import show_conversation_history, clear_quotes
from pages.components.chat import run
from functions.simple_mcp import get_all_servers

# 设置页面为宽页模式
st.set_page_config(layout="wide")

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
    ]
)

# 初始化Streamlit
print(init_streamlit(), "Init Streamlit Successfully!")
init_streamlit()

# 初始化MCP服务
if "mcp_initialized" not in st.session_state:
    # 创建配置目录
    os.makedirs("config", exist_ok=True)

    # 检查已启用的MCP服务
    enabled_count = sum(1 for server in get_all_servers().values() if server.get("enabled", False))

    if enabled_count > 0:
        logging.info(f"发现 {enabled_count} 个已启用的MCP服务")

    st.session_state.mcp_initialized = True

# 页面标题
st.title("Retail Assistant Chat")
st.subheader("Requirement, Research, Plan, Execute")
st.markdown("---")

# 侧边栏：记忆管理
with st.sidebar:
    render_chat_sidebar()

show_conversation_history()

# 处理用户输入
if prompt := st.chat_input("Type something..."):
    history = st.session_state.quoted_history.copy()
    response = run(
        query=prompt,
        # system_input="<memory>\napril 智能体的默认system prompt\n</memory>",
        history=history
    )
    clear_quotes()
