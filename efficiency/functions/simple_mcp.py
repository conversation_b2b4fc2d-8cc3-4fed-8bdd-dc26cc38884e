"""
简化的MCP客户端实现 - 基于官方文档
统一处理STDIO和HTTP类型的MCP服务
"""
import os
import json
import logging
import asyncio
from typing import Dict, Any, List, Optional, Tuple
from contextlib import AsyncExitStack

# 导入MCP相关模块
try:
    from mcp import ClientSession, StdioServerParameters
    from mcp.client.stdio import stdio_client
    MCP_AVAILABLE = True
except ImportError:
    MCP_AVAILABLE = False
    logging.warning("MCP模块不可用，MCP功能将被禁用")

# 导入HTTP请求模块
try:
    import requests
    HTTP_AVAILABLE = True
except ImportError:
    HTTP_AVAILABLE = False
    logging.warning("HTTP模块不可用，远程MCP服务将被禁用")


class SimpleMCPClient:
    """MCP客户端"""

    def __init__(self):
        self.session: Optional[ClientSession] = None
        self.exit_stack = AsyncExitStack()
        # 使用绝对路径，确保在容器中正确工作
        self.config_path = os.path.join(os.getcwd(), "config/mcp_config.json")
        self.config = self._load_config()
        self.stdio = None
        self.write = None

    def _load_config(self) -> Dict[str, Any]:
        """加载MCP配置"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                # 创建默认配置
                default_config = {"mcpServers": {}}
                os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
                with open(self.config_path, 'w', encoding='utf-8') as f:
                    json.dump(default_config, f, indent=2, ensure_ascii=False)
                return default_config
        except Exception as e:
            logging.error(f"加载MCP配置失败: {e}")
            return {"mcpServers": {}}

    def get_server_config(self, server_name: str) -> Optional[Dict[str, Any]]:
        """获取服务器配置"""
        # 重新加载配置以确保获取最新状态
        self.config = self._load_config()
        return self.config.get("mcpServers", {}).get(server_name)

    def get_all_servers(self) -> Dict[str, Dict[str, Any]]:
        """获取所有服务器配置"""
        self.config = self._load_config()
        return self.config.get("mcpServers", {})

    def get_server_status(self, server_name: str) -> str:
        """获取服务器状态"""
        server_config = self.get_server_config(server_name)
        if not server_config:
            return "not_configured"
        if not server_config.get("enabled", False):
            return "disabled"

        # 对于按需启动的服务，显示为ready
        if server_config.get("on_demand", False):
            return "ready"

        # HTTP服务总是ready状态
        if server_config.get("connection_type") == "http":
            return "ready"

        return "ready"

    async def connect_to_server(self, server_name: str) -> bool:
        """连接到MCP服务器"""
        server_config = self.get_server_config(server_name)
        if not server_config or not server_config.get("enabled", False):
            return False

        try:
            connection_type = server_config.get("connection_type", "stdio")

            if connection_type == "http":
                # HTTP连接处理 - 不需要实际连接，调用时建立
                return True
            else:
                # STDIO连接处理
                return await self._connect_stdio_server(server_config)

        except Exception as e:
            logging.error(f"连接到MCP服务器失败: {server_name}, 错误: {e}")
            return False

    async def _connect_stdio_server(self, server_config: Dict[str, Any]) -> bool:
        """连接STDIO服务器"""
        if "command" not in server_config:
            return False

        server_params = StdioServerParameters(
            command=server_config["command"],
            args=server_config.get("args", []),
            env=server_config.get("env")
        )

        stdio_transport = await self.exit_stack.enter_async_context(
            stdio_client(server_params)
        )
        self.stdio, self.write = stdio_transport
        self.session = await self.exit_stack.enter_async_context(
            ClientSession(self.stdio, self.write)
        )

        await self.session.initialize()
        return True

    async def list_tools(self, server_name: str) -> List[Dict[str, Any]]:
        """列出服务器工具"""
        server_config = self.get_server_config(server_name)
        if not server_config:
            return []

        connection_type = server_config.get("connection_type", "stdio")

        if connection_type == "http":
            return await self._list_http_tools(server_config)
        else:
            return await self._list_stdio_tools(server_name)

    async def _list_stdio_tools(self, server_name: str) -> List[Dict[str, Any]]:
        """列出STDIO服务器工具"""
        if not await self.connect_to_server(server_name):
            return []

        try:
            if self.session:
                response = await self.session.list_tools()
                return [{"name": tool.name, "description": tool.description} for tool in response.tools]
        except Exception as e:
            logging.error(f"列出STDIO工具失败: {e}")
        return []

    async def _list_http_tools(self, server_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """列出HTTP服务器工具"""
        server_name = server_config.get("server_name", "")

        if "hotnews" in server_name:
            return [{"name": "get_hot_news", "description": "获取热门新闻"}]
        elif "chinarailway" in server_name:
            return [{"name": "query_trains", "description": "查询火车票"}]
        elif "duckduckgo" in server_name:
            return [{"name": "search", "description": "搜索网页"}]

        return []

    async def call_tool(self, server_name: str, tool_name: str, arguments: Dict[str, Any]) -> Tuple[bool, str]:
        """调用工具"""
        server_config = self.get_server_config(server_name)
        if not server_config:
            return False, f"服务器配置不存在: {server_name}"

        if not server_config.get("enabled", False):
            return False, f"服务器未启用: {server_name}"

        connection_type = server_config.get("connection_type", "stdio")

        try:
            if connection_type == "http":
                return await self._call_http_tool(server_config, tool_name, arguments)
            else:
                return await self._call_stdio_tool(server_name, tool_name, arguments)
        except Exception as e:
            logging.error(f"调用工具失败: {server_name}.{tool_name}, 错误: {e}")
            return False, f"调用失败: {str(e)}"

    async def _call_stdio_tool(self, server_name: str, tool_name: str, arguments: Dict[str, Any]) -> Tuple[bool, str]:
        """调用STDIO工具"""
        if not await self.connect_to_server(server_name):
            return False, "连接失败"

        try:
            if self.session:
                result = await self.session.call_tool(tool_name, arguments)
                # 处理MCP返回的结果
                result_str = str(result.content)

                # 如果结果包含TextContent对象的字符串表示，提取实际文本
                if 'TextContent(' in result_str and 'text=' in result_str:
                    import re
                    # 提取text字段的内容
                    text_match = re.search(r"text='([^']*)'", result_str)
                    if text_match:
                        actual_text = text_match.group(1)
                        # 处理转义字符
                        actual_text = actual_text.replace('\\n', '\n').replace('\\t', '\t').replace('\\r', '\r')
                        return True, actual_text

                # 如果是正常的内容对象，直接提取文本
                if hasattr(result, 'content') and result.content:
                    content_parts = []
                    for content_item in result.content:
                        if hasattr(content_item, 'text'):
                            content_parts.append(content_item.text)
                        else:
                            content_parts.append(str(content_item))
                    return True, '\n'.join(content_parts)

                return True, str(result)
        except Exception as e:
            logging.error(f"调用STDIO工具失败: {e}")
            return False, f"调用失败: {str(e)}"

    async def _call_http_tool(self, server_config: Dict[str, Any], tool_name: str, arguments: Dict[str, Any]) -> Tuple[bool, str]:
        """调用HTTP工具（Smithery远程服务）"""
        # 简化HTTP调用
        api_key = server_config.get("api_key")
        server_name = server_config.get("server_name")

        if not api_key or not server_name:
            return False, "缺少API密钥或服务器名称"

        # 构建请求URL
        base_url = "https://api.smithery.ai/v1/mcp"
        url = f"{base_url}/{server_name}/{tool_name}"

        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

        try:
            response = requests.post(url, json=arguments, headers=headers, timeout=30)
            if response.status_code == 200:
                result = response.json()
                return True, json.dumps(result, ensure_ascii=False, indent=2)
            else:
                return False, f"HTTP错误: {response.status_code} - {response.text}"
        except Exception as e:
            return False, f"HTTP请求失败: {str(e)}"

    async def cleanup(self):
        """清理资源"""
        await self.exit_stack.aclose()


# 全局实例
simple_mcp_client = SimpleMCPClient()


# 统一的MCP工具调用接口
def use_mcp_tool(server_name: str, tool_name: str, arguments: Dict[str, Any]) -> Tuple[bool, str]:
    """使用MCP工具（统一接口）"""
    try:
        # 使用asyncio运行异步函数
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(
                simple_mcp_client.call_tool(server_name, tool_name, arguments)
            )
        finally:
            loop.close()
    except Exception as e:
        logging.error(f"MCP工具调用失败: {e}")
        return False, f"调用失败: {str(e)}"


def get_available_tools(server_name: str) -> Dict[str, Any]:
    """获取可用工具"""
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            tools = loop.run_until_complete(
                simple_mcp_client.list_tools(server_name)
            )
            return {"success": True, "tools": tools}
        finally:
            loop.close()
    except Exception as e:
        logging.error(f"获取工具列表失败: {e}")
        return {"success": False, "error": str(e), "tools": []}


def get_server_status(server_name: str) -> str:
    """获取服务器状态"""
    return simple_mcp_client.get_server_status(server_name)


def get_all_servers() -> Dict[str, Dict[str, Any]]:
    """获取所有服务器配置"""
    return simple_mcp_client.get_all_servers()
